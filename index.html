<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lookahead Trainer</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f0f0f0;
      display: flex;
    }

    .sidebar {
      width: 280px;
      min-width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px;
      box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      overflow-y: auto;
      height: 100vh;
      box-sizing: border-box;
    }

    .main-content {
      flex: 1;
      position: relative;
      height: 100vh;
      overflow: hidden;
      background: #2c3e50;
    }

    canvas {
      display: block;
      width: 100% !important;
      height: 100% !important;
    }

    .control-panel {
      background: rgba(255, 255, 255, 0.1);
      padding: 12px;
      border-radius: 8px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      margin-bottom: 12px;
    }

    .sidebar h2 {
      margin: 0 0 15px 0;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .timer-display {
      position: absolute;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      color: #00ff88;
      padding: 12px 20px;
      border-radius: 10px;
      border: 2px solid #00ff88;
      box-shadow: 0 4px 16px rgba(0, 255, 136, 0.3);
      font-size: 28px;
      font-weight: bold;
      font-family: 'Courier New', monospace;
      z-index: 1000;
      min-width: 140px;
      text-align: center;
      transition: all 0.3s ease;
    }

    .timer-display.hidden {
      opacity: 0;
      transform: translateY(-10px);
      pointer-events: none;
    }

    .scramble-overlay {
      position: absolute;
      top: 20px;
      left: 20px;
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 15px 25px;
      border-radius: 10px;
      border: 2px solid #f39c12;
      box-shadow: 0 4px 16px rgba(243, 156, 18, 0.3);
      font-size: 20px;
      font-weight: bold;
      font-family: 'Courier New', monospace;
      z-index: 1000;
      max-width: 400px;
      text-align: center;
      transition: all 0.3s ease;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .scramble-overlay.hidden {
      opacity: 0;
      transform: translateY(-10px);
      pointer-events: none;
    }

    .control-section {
      margin-bottom: 10px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .control-section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .control-section h3 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      opacity: 0.9;
    }

    .button-group {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
      margin-top: 6px;
    }

    button {
      padding: 8px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 600;
      transition: all 0.2s ease;
      text-transform: uppercase;
      letter-spacing: 0.3px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      flex: 1;
      min-width: 0;
    }

    button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none !important;
    }

    button:not(:disabled):hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
    }

    .btn-danger {
      background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
      color: white;
    }

    .input-group {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 6px;
    }

    input[type="number"] {
      width: 50px;
      padding: 4px 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      font-size: 12px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      backdrop-filter: blur(10px);
    }

    input[type="number"]:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.6);
      background: rgba(255, 255, 255, 0.2);
    }

    input[type="checkbox"] {
      margin-right: 6px;
      transform: scale(1.1);
    }

    .status-display {
      background: rgba(255, 255, 255, 0.15);
      padding: 10px;
      border-radius: 6px;
      border-left: 3px solid #00ff88;
      font-size: 12px;
      line-height: 1.4;
      backdrop-filter: blur(10px);
      color: white;
    }

    .scramble-display {
      background: rgba(255, 193, 7, 0.2);
      padding: 10px;
      border-radius: 6px;
      border-left: 3px solid #ffc107;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      font-weight: bold;
      word-break: break-all;
      backdrop-filter: blur(10px);
      color: white;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .hidden {
      display: none !important;
    }

    label {
      font-size: 12px;
      font-weight: 500;
      color: white;
      opacity: 0.9;
    }
  </style>
</head>
<body>
  <!-- Sidebar -->
  <div class="sidebar">
    <h2>🎯 Lookahead Trainer</h2>

    <!-- Settings Section -->
    <div class="control-panel">
      <h3>⚙️ Settings</h3>
      <label>
        <input type="checkbox" id="toggleColors" checked> Show colors
      </label>
      <div class="input-group">
        <label for="scrambleLength">Scramble length:</label>
        <input type="number" id="scrambleLength" value="4" min="1" max="20">
      </div>
    </div>

    <!-- Game Controls -->
    <div class="control-panel">
      <h3>🎮 Controls</h3>
      <div class="button-group">
        <button class="btn-primary" onclick="trainer.selectRandomPiece()" id="randomButton">Random Start</button>
        <button class="btn-success hidden" onclick="trainer.startRound()" id="startButton">Start Round</button>
        <button class="btn-warning hidden" onclick="trainer.revealAnswer()" id="revealButton">Reveal Answer</button>
        <button class="btn-danger" onclick="trainer.reset()" id="resetButton">Reset</button>
      </div>
    </div>

    <!-- Status Display -->
    <div class="control-panel">
      <h3>📊 Status</h3>
      <div class="status-display" id="status">Click a piece/sticker to select it, or use "Random Start"</div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <!-- Timer Display -->
    <div class="timer-display hidden" id="timerDisplay">00:00.0</div>

    <!-- Scramble Overlay -->
    <div class="scramble-overlay hidden" id="scrambleOverlay"></div>

    <!-- 3D Canvas will be inserted here -->
  </div>

  <script type="importmap">
  {
    "imports": {
      "three": "./three/build/three.module.js",
      "three/addons/": "./three/examples/jsm/"
    }
  }
  </script>

  <script type="module">
    import * as THREE from 'three';
    import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
    import { FontLoader } from 'three/addons/loaders/FontLoader.js';
    import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

    // ======================
    // CONSTANTS & CONFIG
    // ======================

    const CONFIG = {
      CUBE_SPACING: 1.05,
      STICKER_OFFSET: 0.501,
      STICKER_SIZE: 0.9,
      ANIMATION_STEPS: 15,
      PULSE_SPEED: 0.005
    };

    const FACE_COLORS = {
      X: { "1": 0xff0000, "-1": 0xff8000 },  // Red/Orange
      Y: { "1": 0xffffff, "-1": 0xffff00 },  // White/Yellow
      Z: { "1": 0x00ff00, "-1": 0x0000ff }   // Green/Blue
    };

    const MOVE_MAPPING = {
      U: ['Y', 1], D: ['Y', -1],
      R: ['X', 1], L: ['X', -1],
      F: ['Z', 1], B: ['Z', -1]
    };

    const FACE_LABELS = {
      U: { axis: 'Y', value: 1, label: 'U' },
      D: { axis: 'Y', value: -1, label: 'D' },
      F: { axis: 'Z', value: 1, label: 'F' },
      B: { axis: 'Z', value: -1, label: 'B' },
      L: { axis: 'X', value: -1, label: 'L' },
      R: { axis: 'X', value: 1, label: 'R' }
    };

    // ======================
    // CUBE STATE REPRESENTATION
    // ======================

    // Edge positions (12 edges)
    const EDGE_POSITIONS = [
      'UF', 'UR', 'UB', 'UL',  // Top layer edges
      'DF', 'DR', 'DB', 'DL',  // Bottom layer edges
      'FR', 'FL', 'BR', 'BL'   // Middle layer edges
    ];

    // Corner positions (8 corners)
    const CORNER_POSITIONS = [
      'UFR', 'UFL', 'UBL', 'UBR',  // Top layer corners
      'DFR', 'DFL', 'DBL', 'DBR'   // Bottom layer corners
    ];

    // Map 3D positions to logical positions
    const POSITION_TO_EDGE = {
      '0,1,1': 'UF',   '1,1,0': 'UR',   '0,1,-1': 'UB',   '-1,1,0': 'UL',
      '0,-1,1': 'DF',  '1,-1,0': 'DR',  '0,-1,-1': 'DB',  '-1,-1,0': 'DL',
      '1,0,1': 'FR',   '-1,0,1': 'FL',  '1,0,-1': 'BR',   '-1,0,-1': 'BL'
    };

    const POSITION_TO_CORNER = {
      '1,1,1': 'UFR',    '-1,1,1': 'UFL',   '-1,1,-1': 'UBL',   '1,1,-1': 'UBR',
      '1,-1,1': 'DFR',   '-1,-1,1': 'DFL',  '-1,-1,-1': 'DBL',  '1,-1,-1': 'DBR'
    };

    // Reverse mappings
    const EDGE_TO_POSITION = Object.fromEntries(
      Object.entries(POSITION_TO_EDGE).map(([pos, edge]) => [edge, pos.split(',').map(Number)])
    );

    const CORNER_TO_POSITION = Object.fromEntries(
      Object.entries(POSITION_TO_CORNER).map(([pos, corner]) => [corner, pos.split(',').map(Number)])
    );

    // ======================
    // STICKER FACE DEFINITIONS
    // ======================

    // Define which faces each edge position has
    const EDGE_FACES = {
      'UF': ['U', 'F'], 'UR': ['U', 'R'], 'UB': ['U', 'B'], 'UL': ['U', 'L'],
      'DF': ['D', 'F'], 'DR': ['D', 'R'], 'DB': ['D', 'B'], 'DL': ['D', 'L'],
      'FR': ['F', 'R'], 'FL': ['F', 'L'], 'BR': ['B', 'R'], 'BL': ['B', 'L']
    };

    // Define which faces each corner position has
    const CORNER_FACES = {
      'UFR': ['U', 'F', 'R'], 'UFL': ['U', 'F', 'L'], 'UBL': ['U', 'B', 'L'], 'UBR': ['U', 'B', 'R'],
      'DFR': ['D', 'F', 'R'], 'DFL': ['D', 'F', 'L'], 'DBL': ['D', 'B', 'L'], 'DBR': ['D', 'B', 'R']
    };

    // ======================
    // CUBE STATE CLASS
    // ======================

    class CubeState {
      constructor() {
        this.reset();
      }

      reset() {
        // Initialize solved state
        // Each edge is at its home position with orientation 0
        this.edges = {};
        EDGE_POSITIONS.forEach((pos, i) => {
          this.edges[pos] = { piece: pos, orientation: 0 };
        });

        // Each corner is at its home position with orientation 0
        this.corners = {};
        CORNER_POSITIONS.forEach((pos, i) => {
          this.corners[pos] = { piece: pos, orientation: 0 };
        });
      }

      // Get the piece type and logical position from 3D coordinates
      getPieceInfo(position) {
        const posKey = position.join(',');

        if (POSITION_TO_EDGE[posKey]) {
          return {
            type: 'edge',
            logicalPosition: POSITION_TO_EDGE[posKey],
            piece: this.edges[POSITION_TO_EDGE[posKey]]
          };
        }

        if (POSITION_TO_CORNER[posKey]) {
          return {
            type: 'corner',
            logicalPosition: POSITION_TO_CORNER[posKey],
            piece: this.corners[POSITION_TO_CORNER[posKey]]
          };
        }

        return null;
      }

      // Apply a move to the cube state
      applyMove(move) {
        const face = move[0];
        const isPrime = move.includes("'");
        const isDouble = move.includes('2');

        let rotations = 1;
        if (isDouble) rotations = 2;

        for (let i = 0; i < rotations; i++) {
          this.rotateFace(face, isPrime);
        }
      }

      rotateFace(face, isPrime) {
        // Define the cycles for each face
        const cycles = this.getFaceCycles(face);

        // For prime moves, we need to cycle in the opposite direction
        if (isPrime) {
          // Apply edge cycle in reverse (3 times clockwise = 1 counterclockwise)
          if (cycles.edges.length > 0) {
            for (let i = 0; i < 3; i++) {
              this.cycleEdges(cycles.edges, cycles.edgeFlip || false);
            }
          }

          // Apply corner cycle in reverse (3 times clockwise = 1 counterclockwise)
          if (cycles.corners.length > 0) {
            for (let i = 0; i < 3; i++) {
              this.cycleCorners(cycles.corners, cycles.cornerRotation || 0);
            }
          }
        } else {
          // Apply normal clockwise cycle
          if (cycles.edges.length > 0) {
            this.cycleEdges(cycles.edges, cycles.edgeFlip || false);
          }

          if (cycles.corners.length > 0) {
            this.cycleCorners(cycles.corners, cycles.cornerRotation || 0);
          }
        }
      }

      getFaceCycles(face) {
        // Define cycles for clockwise moves (looking at the face)
        // For prime moves, these will be reversed
        const cycles = {
          U: {
            edges: ['UF', 'UL', 'UB', 'UR'], // Clockwise: UF→UL→UB→UR→UF
            corners: ['UFR', 'UFL', 'UBL', 'UBR'], // Clockwise: UFR→UFL→UBL→UBR→UFR
            edgeFlip: false,
            cornerRotation: 0
          },
          D: {
            edges: ['DF', 'DR', 'DB', 'DL'], // Clockwise: DF→DR→DB→DL→DF
            corners: ['DFR', 'DBR', 'DBL', 'DFL'], // Clockwise: DFR→DBR→DBL→DFL→DFR
            edgeFlip: false,
            cornerRotation: 0
          },
          R: {
            edges: ['UR', 'BR', 'DR', 'FR'], // Clockwise: UR→BR→DR→FR→UR
            corners: ['UFR', 'UBR', 'DBR', 'DFR'], // Clockwise: UFR→UBR→DBR→DFR→UFR
            edgeFlip: false,
            cornerRotation: 1
          },
          L: {
            edges: ['UL', 'FL', 'DL', 'BL'], // Clockwise: UL→FL→DL→BL→UL
            corners: ['UFL', 'DFL', 'DBL', 'UBL'], // Clockwise: UFL→DFL→DBL→UBL→UFL
            edgeFlip: false,
            cornerRotation: 2
          },
          F: {
            edges: ['UF', 'FR', 'DF', 'FL'], // Clockwise: UF→FR→DF→FL→UF
            corners: ['UFR', 'DFR', 'DFL', 'UFL'], // Clockwise: UFR→DFR→DFL→UFL→UFR
            edgeFlip: true,
            cornerRotation: 1
          },
          B: {
            edges: ['UB', 'BL', 'DB', 'BR'], // Clockwise: UB→BL→DB→BR→UB
            corners: ['UBR', 'DBR', 'DBL', 'UBL'], // Clockwise: UBR→DBR→DBL→UBL→UBR
            edgeFlip: true,
            cornerRotation: 2
          }
        };

        return cycles[face] || { edges: [], corners: [] };
      }

      cycleEdges(positions, flip) {
        if (positions.length < 2) return;

        // Store the last piece
        const temp = { ...this.edges[positions[positions.length - 1]] };

        // Shift all pieces
        for (let i = positions.length - 1; i > 0; i--) {
          this.edges[positions[i]] = { ...this.edges[positions[i - 1]] };
          if (flip) {
            this.edges[positions[i]].orientation = 1 - this.edges[positions[i]].orientation;
          }
        }

        // Place the temp piece at the first position
        this.edges[positions[0]] = temp;
        if (flip) {
          this.edges[positions[0]].orientation = 1 - this.edges[positions[0]].orientation;
        }
      }

      cycleCorners(positions, rotation) {
        if (positions.length < 2) return;

        // Store the last piece
        const temp = { ...this.corners[positions[positions.length - 1]] };

        // Shift all pieces
        for (let i = positions.length - 1; i > 0; i--) {
          this.corners[positions[i]] = { ...this.corners[positions[i - 1]] };
          if (rotation !== 0) {
            this.corners[positions[i]].orientation = (this.corners[positions[i]].orientation + rotation) % 3;
          }
        }

        // Place the temp piece at the first position
        this.corners[positions[0]] = temp;
        if (rotation !== 0) {
          this.corners[positions[0]].orientation = (this.corners[positions[0]].orientation + rotation) % 3;
        }
      }

      // Find where a piece ends up after moves
      findPiece(originalPiece) {
        // Look through all positions to find where this piece ended up
        for (const [position, piece] of Object.entries(this.edges)) {
          if (piece.piece === originalPiece) {
            return {
              type: 'edge',
              logicalPosition: position,
              orientation: piece.orientation,
              coordinates: EDGE_TO_POSITION[position]
            };
          }
        }

        for (const [position, piece] of Object.entries(this.corners)) {
          if (piece.piece === originalPiece) {
            return {
              type: 'corner',
              logicalPosition: position,
              orientation: piece.orientation,
              coordinates: CORNER_TO_POSITION[position]
            };
          }
        }

        return null;
      }

      // Find where a specific sticker face ends up after moves
      findStickerFace(originalPiece, originalFace) {
        const pieceLocation = this.findPiece(originalPiece);
        if (!pieceLocation) return null;

        const finalFace = this.transformStickerFace(
          originalPiece,
          originalFace,
          pieceLocation.logicalPosition,
          pieceLocation.orientation,
          pieceLocation.type
        );

        return {
          ...pieceLocation,
          finalFace: finalFace
        };
      }

      // Transform a sticker face based on piece movement and orientation
      transformStickerFace(originalPiece, originalFace, finalPosition, finalOrientation, pieceType) {
        if (pieceType === 'edge') {
          return this.transformEdgeStickerFace(originalPiece, originalFace, finalPosition, finalOrientation);
        } else if (pieceType === 'corner') {
          return this.transformCornerStickerFace(originalPiece, originalFace, finalPosition, finalOrientation);
        }
        return originalFace;
      }

      transformEdgeStickerFace(originalPiece, originalFace, finalPosition, finalOrientation) {
        // Get the faces available at the original and final positions
        const originalFaces = EDGE_FACES[originalPiece];
        const finalFaces = EDGE_FACES[finalPosition];

        if (!originalFaces || !finalFaces) return originalFace;

        // Find which index the original face was at
        const originalIndex = originalFaces.indexOf(originalFace);
        if (originalIndex === -1) return originalFace;

        // For edges: orientation 0 = normal, 1 = flipped
        let finalIndex = originalIndex;
        if (finalOrientation === 1) {
          // Flipped: swap the two faces
          finalIndex = 1 - originalIndex;
        }

        // Map to the corresponding face at the final position
        return finalFaces[finalIndex] || originalFace;
      }

      transformCornerStickerFace(originalPiece, originalFace, finalPosition, finalOrientation) {
        // Get the faces available at the original and final positions
        const originalFaces = CORNER_FACES[originalPiece];
        const finalFaces = CORNER_FACES[finalPosition];

        if (!originalFaces || !finalFaces) return originalFace;

        // Find which index the original face was at
        const originalIndex = originalFaces.indexOf(originalFace);
        if (originalIndex === -1) return originalFace;

        // SIMPLIFIED APPROACH: For now, let's map faces directly based on position
        // This handles the case where a piece moves but doesn't twist

        // Create a mapping from original position faces to final position faces
        // UFR: ['U', 'F', 'R'] -> DFR: ['D', 'F', 'R']
        // The F and R faces stay the same, U becomes D

        let mappedFace = originalFace;

        // Handle position-based face mapping first
        if (originalPiece !== finalPosition) {
          // Map faces based on position change
          if (originalPiece === 'UFR' && finalPosition === 'DFR') {
            if (originalFace === 'U') mappedFace = 'D';
            else if (originalFace === 'F') mappedFace = 'F';
            else if (originalFace === 'R') mappedFace = 'R';
          }
          // Add more position mappings as needed
          else {
            // Generic mapping: try to find corresponding face
            const originalFaceIndex = originalFaces.indexOf(originalFace);
            if (originalFaceIndex !== -1 && finalFaces[originalFaceIndex]) {
              mappedFace = finalFaces[originalFaceIndex];
            }
          }
        }

        // Then apply orientation twist
        if (finalOrientation !== 0) {
          const finalFaceIndex = finalFaces.indexOf(mappedFace);
          if (finalFaceIndex !== -1) {
            const rotatedIndex = (finalFaceIndex + finalOrientation) % 3;
            mappedFace = finalFaces[rotatedIndex];
          }
        }

        console.log('Corner sticker transformation:', {
          originalPiece,
          originalFace,
          finalPosition,
          finalOrientation,
          originalFaces,
          finalFaces,
          originalIndex,
          mappedFace,
          result: mappedFace
        });

        return mappedFace;
      }
    }

    // ======================
    // SCENE SETUP
    // ======================

    class CubeRenderer {
      constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.controls = null;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();

        this.init();
      }

      init() {
        // Camera setup
        this.camera.position.set(2.6, 2.6, 6.4);
        this.camera.lookAt(this.scene.position);

        // Renderer setup - fill the entire main content area
        const availableWidth = window.innerWidth - 280; // Account for sidebar
        const availableHeight = window.innerHeight;

        this.renderer.setSize(availableWidth, availableHeight);
        document.querySelector('.main-content').appendChild(this.renderer.domElement);

        // Controls setup
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.1;
        this.controls.rotateSpeed = 0.5;
        this.controls.zoomSpeed = 0.5;

        // Lighting setup
        this.setupLighting();

        // Event listeners
        this.setupEventListeners();
      }

      setupLighting() {
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 7.5);
        this.scene.add(directionalLight);

        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        this.scene.add(ambientLight);
      }

      setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('click', (event) => this.onClick(event));
      }

      onWindowResize() {
        const availableWidth = window.innerWidth - 280; // Account for sidebar
        const availableHeight = window.innerHeight;

        this.camera.aspect = availableWidth / availableHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(availableWidth, availableHeight);
      }

      onClick(event) {
        // Adjust for sidebar - only consider clicks in the main content area
        const mainContentRect = document.querySelector('.main-content').getBoundingClientRect();
        this.mouse.x = ((event.clientX - mainContentRect.left) / mainContentRect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - mainContentRect.top) / mainContentRect.height) * 2 + 1;

        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);

        if (intersects.length > 0) {
          cube.handleCubeletClick(intersects[0]);
        }
      }

      render() {
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
      }

      resetCamera() {
        // Reset camera to initial position and orientation
        this.camera.position.set(2.6, 2.6, 6.4);
        this.camera.lookAt(this.scene.position);
        this.controls.reset();
      }
    }

    // ======================
    // CUBE LOGIC
    // ======================

    class RubiksCube {
      constructor(renderer) {
        this.renderer = renderer;
        this.cubelets = [];
        this.selectedCubelet = null;
        this.trackedCubelet = null;
        this.animating = false;
        this.cubeState = new CubeState(); // Add logical cube state tracking

        this.createCube();
        this.addFaceLabels();
      }

      createCube() {
        // Create all 26 pieces (excluding center)
        for (let x = -1; x <= 1; x++) {
          for (let y = -1; y <= 1; y++) {
            for (let z = -1; z <= 1; z++) {
              if (x === 0 && y === 0 && z === 0) continue; // Skip center

              this.createCubelet(x, y, z);
            }
          }
        }
      }

      createCubelet(x, y, z) {
        // Create base cubelet
        const cubelet = new THREE.Mesh(
          new THREE.BoxGeometry(1, 1, 1),
          new THREE.MeshLambertMaterial({ color: 0x222222 })
        );

        cubelet.position.set(x * CONFIG.CUBE_SPACING, y * CONFIG.CUBE_SPACING, z * CONFIG.CUBE_SPACING);

        // Determine logical position from 3D coordinates
        const position3D = [x, y, z];
        const posKey = position3D.join(',');
        let logicalPosition = null;
        let pieceType = null;

        if (POSITION_TO_EDGE[posKey]) {
          logicalPosition = POSITION_TO_EDGE[posKey];
          pieceType = 'edge';
        } else if (POSITION_TO_CORNER[posKey]) {
          logicalPosition = POSITION_TO_CORNER[posKey];
          pieceType = 'corner';
        }

        cubelet.userData = {
          position: [x, y, z], // 3D position for rendering
          logicalPosition: logicalPosition, // Logical position (FR, UFL, etc.)
          pieceType: pieceType, // 'edge' or 'corner'
          originalLogicalPosition: logicalPosition, // Never changes
          orientation: 0
        };

        // Add stickers to visible faces
        if (x !== 0) this.addSticker(cubelet, 'X', x, logicalPosition);
        if (y !== 0) this.addSticker(cubelet, 'Y', y, logicalPosition);
        if (z !== 0) this.addSticker(cubelet, 'Z', z, logicalPosition);

        this.renderer.scene.add(cubelet);
        this.cubelets.push(cubelet);
      }

      addSticker(parent, axis, direction, pieceId) {
        const showColors = document.getElementById('toggleColors')?.checked ?? true;
        const color = showColors ? FACE_COLORS[axis][direction.toString()] : 0xcccccc;

        const material = new THREE.MeshBasicMaterial({
          color,
          side: THREE.DoubleSide,
          depthTest: true,
          depthWrite: true
        });
        material.userData = { baseColor: color };

        const geometry = new THREE.PlaneGeometry(CONFIG.STICKER_SIZE, CONFIG.STICKER_SIZE);
        const sticker = new THREE.Mesh(geometry, material);

        // Store sticker info including piece ID and face
        sticker.userData = {
          pieceId: pieceId,
          face: axis,
          direction: direction,
          originalFace: { axis, direction } // Never changes
        };

        // Position sticker on correct face
        this.positionSticker(sticker, axis, direction);
        parent.add(sticker);
      }

      positionSticker(sticker, axis, direction) {
        const offset = CONFIG.STICKER_OFFSET;

        switch (axis) {
          case 'X':
            sticker.rotation.y = Math.PI / 2;
            sticker.position.x = direction * offset;
            break;
          case 'Y':
            sticker.rotation.x = -Math.PI / 2;
            sticker.position.y = direction * offset;
            break;
          case 'Z':
            sticker.position.z = direction * offset;
            break;
        }
      }

      addFaceLabels() {
        for (const key in FACE_LABELS) {
          const { axis, value, label } = FACE_LABELS[key];
          const sprite = this.createFaceLabel(label);

          sprite.position[axis.toLowerCase()] = value * 1.75;

          // Try to attach to center piece, otherwise add to scene
          const center = this.cubelets.find(c =>
            c.userData.position[this.axisToIndex(axis)] === value &&
            c.userData.position.filter(v => v === 0).length === 2
          );

          if (center) {
            center.add(sprite);
          } else {
            this.renderer.scene.add(sprite);
          }
        }
      }

      createFaceLabel(text) {
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 128;
        const ctx = canvas.getContext('2d');

        ctx.font = 'bold 64px sans-serif';
        ctx.fillStyle = 'black';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 4;
        ctx.strokeText(text, 64, 64);
        ctx.fillText(text, 64, 64);

        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;

        const material = new THREE.SpriteMaterial({
          map: texture,
          depthTest: true,
          depthWrite: false
        });

        const sprite = new THREE.Sprite(material);
        sprite.renderOrder = 999;
        sprite.scale.set(0.9, 0.9, 1);

        return sprite;
      }

      // ======================
      // SELECTION & INTERACTION
      // ======================

      handleCubeletClick(intersect) {
        const intersected = intersect.object;
        const parentCubelet = intersected.parent.type === 'Mesh' ? intersected.parent : intersected;

        // Skip invalid selections
        if (!this.isValidCubelet(parentCubelet)) return;

        // Handle training mode clicks - check for sticker clicks during guessing
        if (trainer.awaitingUserGuess && this.selectedCubelet) {
          // User is making a guess - check if they clicked a sticker
          if (intersected.type === 'Mesh' && intersected.geometry.type === 'PlaneGeometry') {
            trainer.handleStickerGuess(parentCubelet, intersected);
            return;
          }
        }

        // Update selection (only when not in guessing mode)
        if (!trainer.awaitingUserGuess) {
          this.updateSelection(parentCubelet, intersected);
          // Notify trainer that a piece was selected
          trainer.onPieceSelected();
        }
      }

      isValidCubelet(cubelet) {
        if (!cubelet.userData || !cubelet.userData.position) return false;

        // Skip center pieces (only 1 axis non-zero)
        const nonZeroAxes = cubelet.userData.position.filter(v => v !== 0);
        return nonZeroAxes.length > 1;
      }

      updateSelection(cubelet, sticker) {
        // Clear previous selection
        this.clearSelection();

        // Set new selection
        this.selectedCubelet = cubelet;
        this.trackedCubelet = cubelet;

        // Store the actual color based on the sticker's face, not current display color
        const axis = this.getStickerAxis(sticker);
        const direction = Math.sign(sticker.position[axis.toLowerCase()]);
        const actualColor = FACE_COLORS[axis][direction.toString()];

        sticker.userData.originalColor = actualColor;
        cubelet.userData.originalSticker = sticker;

        // Store which face this sticker is on for tracking
        cubelet.userData.selectedStickerFace = this.getStickerFaceInfo(sticker);
      }

      getStickerFaceInfo(sticker) {
        // Determine which face this sticker is on
        const axis = this.getStickerAxis(sticker);
        const direction = Math.sign(sticker.position[axis.toLowerCase()]);
        return { axis, direction };
      }

      clearSelection() {
        if (!this.selectedCubelet) return;

        // Reset cubelet appearance
        this.selectedCubelet.material.color.setHex(0x222222);
        this.selectedCubelet.material.emissive.setHex(0x000000);
        this.selectedCubelet.material.emissiveIntensity = 0;
        this.selectedCubelet.material.needsUpdate = true;

        // Reset sticker appearance based on current color mode
        const sticker = this.selectedCubelet.userData.originalSticker;
        if (sticker && sticker.material) {
          const showColors = document.getElementById('toggleColors')?.checked ?? true;
          if (showColors && sticker.userData.originalColor !== undefined) {
            sticker.material.color.setHex(sticker.userData.originalColor);
          } else {
            // When colors are off, reset to grey
            sticker.material.color.setHex(0xcccccc);
          }
        }

        // Clean up references
        if (this.selectedCubelet.userData) {
          this.selectedCubelet.userData.originalSticker = null;
        }
      }

      // ======================
      // CUBE MOVES & ANIMATION
      // ======================

      applyMoves(moves) {
        if (this.animating) return;

        const sequence = typeof moves === 'string' ? moves.split(' ') : moves;
        let index = 0;

        const executeNextMove = () => {
          if (index >= sequence.length) {
            this.animating = false;
            this.highlightTrackedPiece();
            return;
          }

          const move = sequence[index++];
          this.executeMove(move, executeNextMove);
        };

        this.animating = true;
        executeNextMove();
      }

      executeMove(move, callback) {
        const times = move.endsWith('2') ? 2 : 1;
        const baseMove = move.replace('2', '');
        let count = 0;

        const repeatMove = () => {
          if (count++ < times) {
            // Update logical cube state
            this.cubeState.applyMove(baseMove);
            this.rotateLayer(baseMove, repeatMove);
          } else {
            callback();
          }
        };

        repeatMove();
      }

      rotateLayer(move, callback) {
        const base = move[0];
        const prime = move.includes("'");
        const [axis, layerIndex] = MOVE_MAPPING[base];

        // Get cubelets in this layer
        const threshold = 0.01;
        const layer = this.cubelets.filter(c =>
          Math.abs(c.userData.position[this.axisToIndex(axis)] - layerIndex) < threshold
        );

        // Create rotation group
        const group = new THREE.Group();
        layer.forEach(c => group.add(c));
        this.renderer.scene.add(group);

        // Calculate rotation
        const invertedFaces = ['D', 'L', 'B'];
        const isInverted = invertedFaces.includes(base);
        const angle = (Math.PI / 2) * (prime ? 1 : -1) * (isInverted ? -1 : 1);
        const rotAxis = new THREE.Vector3(
          axis === 'X' ? 1 : 0,
          axis === 'Y' ? 1 : 0,
          axis === 'Z' ? 1 : 0
        );

        this.animateRotation(group, layer, rotAxis, angle, callback);
      }

      animateRotation(group, layer, rotAxis, totalAngle, callback) {
        let currentStep = 0;
        const stepAngle = totalAngle / CONFIG.ANIMATION_STEPS;

        const animateStep = () => {
          if (currentStep++ < CONFIG.ANIMATION_STEPS) {
            group.rotateOnAxis(rotAxis, stepAngle);
            requestAnimationFrame(animateStep);
          } else {
            this.finishRotation(group, layer);
            if (callback) callback();
          }
        };

        animateStep();
      }

      finishRotation(group, layer) {
        group.updateMatrixWorld(true);

        layer.forEach(cubelet => {
          // Update orientation for edge pieces
          this.updateCubeletOrientation(cubelet, group);

          // Apply transformation and update position
          cubelet.applyMatrix4(group.matrixWorld);
          cubelet.matrixAutoUpdate = true;
          this.renderer.scene.add(cubelet);

          // Update logical position with better rounding
          const newX = Math.round(cubelet.position.x / CONFIG.CUBE_SPACING * 100) / 100;
          const newY = Math.round(cubelet.position.y / CONFIG.CUBE_SPACING * 100) / 100;
          const newZ = Math.round(cubelet.position.z / CONFIG.CUBE_SPACING * 100) / 100;

          cubelet.userData.position = [
            Math.round(newX),
            Math.round(newY),
            Math.round(newZ)
          ];

          // Ensure position values are exactly -1, 0, or 1
          cubelet.userData.position = cubelet.userData.position.map(val => {
            if (Math.abs(val) < 0.1) return 0;
            return val > 0 ? 1 : -1;
          });
        });

        this.renderer.scene.remove(group);
      }

      updateCubeletOrientation(cubelet, group) {
        // Determine if this is an edge piece and should flip orientation
        const pos = cubelet.userData.position;
        const nonZeroAxes = pos.map((v, i) => v !== 0 ? i : -1).filter(i => i !== -1);
        const isEdge = nonZeroAxes.length === 2;

        // Get rotation axis from group's rotation
        // This is a simplified version - in practice you'd track the rotation axis
        // For now, we'll use a heuristic based on the move being performed
        if (isEdge) {
          // Flip orientation for edge pieces in certain rotations
          // This is a simplified implementation
          cubelet.userData.orientation ^= 1;
        }
      }

      highlightTrackedPiece() {
        if (this.selectedCubelet) {
          this.selectedCubelet.material.color.set(0x00ff00);
        }
      }

      // ======================
      // COLOR MANAGEMENT
      // ======================

      toggleColors() {
        const showColors = document.getElementById('toggleColors').checked;

        this.cubelets.forEach(cubelet => {
          cubelet.children.forEach(sticker => {
            if (!sticker.material) return;

            const axis = this.getStickerAxis(sticker);
            const direction = Math.sign(sticker.position[axis.toLowerCase()]);
            const color = showColors ? FACE_COLORS[axis][direction.toString()] : 0xcccccc;

            this.animateColorTransition(sticker, color);
          });
        });
      }

      getStickerAxis(sticker) {
        if (Math.abs(sticker.position.x) > 0.5) return 'X';
        if (Math.abs(sticker.position.y) > 0.5) return 'Y';
        return 'Z';
      }

      animateColorTransition(sticker, targetColor) {
        const currentColor = sticker.material.color.clone();
        const newColor = new THREE.Color(targetColor);
        let progress = 0;
        const duration = 300;
        const steps = 30;
        const stepTime = duration / steps;

        const animate = () => {
          progress += 1 / steps;
          sticker.material.color.lerpColors(currentColor, newColor, progress);
          if (progress < 1) {
            setTimeout(animate, stepTime);
          }
        };

        animate();
      }

      // ======================
      // UTILITY METHODS
      // ======================

      axisToIndex(axis) {
        return { X: 0, Y: 1, Z: 2 }[axis];
      }

      snapToAxis(value) {
        const epsilon = 0.1;
        if (Math.abs(value - CONFIG.CUBE_SPACING) < epsilon) return 1;
        if (Math.abs(value + CONFIG.CUBE_SPACING) < epsilon) return -1;
        if (Math.abs(value) < epsilon) return 0;
        return NaN;
      }

      reset(clearSelection = true) {
        if (clearSelection) {
          this.clearSelection();
          this.selectedCubelet = null;
          this.trackedCubelet = null;
        }

        // Reset all cubelets to default state
        this.cubelets.forEach(c => {
          c.material.color.set(0x222222);
          c.material.emissive.setHex(0x000000);
          c.material.emissiveIntensity = 0;
          c.userData.orientation = 0;
        });

        // Reset logical cube state
        this.cubeState.reset();

        // Reset camera position
        this.renderer.resetCamera();

        // Reset cube to solved state by recreating it
        // This is more reliable than trying to track all moves
        this.resetToSolvedState();
      }

      resetToSolvedState() {
        // Remove all existing cubelets
        this.cubelets.forEach(cubelet => {
          this.renderer.scene.remove(cubelet);
        });
        this.cubelets = [];

        // Recreate the cube in solved state
        this.createCube();
        this.addFaceLabels();
      }

      // Animation update for selected piece pulsing
      updateAnimation() {
        if (this.selectedCubelet && this.selectedCubelet.userData.originalSticker) {
          const t = Date.now() * CONFIG.PULSE_SPEED;
          const blend = 0.5 + 0.5 * Math.sin(t);

          const sticker = this.selectedCubelet.userData.originalSticker;

          // Determine the base color based on current color mode
          const showColors = document.getElementById('toggleColors')?.checked ?? true;
          let baseColor;

          if (showColors) {
            baseColor = new THREE.Color(sticker.userData.originalColor);
          } else {
            baseColor = new THREE.Color(0xcccccc); // Grey when colors are off
          }

          const targetColor = new THREE.Color(0xba55d3); // Purple highlight
          sticker.material.color.copy(baseColor).lerp(targetColor, blend);
        }

        if (this.selectedCubelet) {
          const t = Date.now() * CONFIG.PULSE_SPEED;
          const intensity = 0.5 + 0.25 * Math.sin(t);
          this.selectedCubelet.material.emissive = new THREE.Color(0xba55d3);
          this.selectedCubelet.material.emissiveIntensity = intensity;
          this.selectedCubelet.material.needsUpdate = true;
        }
      }
    }

    // ======================
    // TIMER SYSTEM
    // ======================

    class GameTimer {
      constructor() {
        this.startTime = null;
        this.isRunning = false;
        this.display = document.getElementById('timerDisplay');
        this.updateInterval = null;
        this.hide(); // Start hidden
      }

      start() {
        this.startTime = Date.now();
        this.isRunning = true;
        this.show();
        this.updateInterval = setInterval(() => this.updateDisplay(), 100);
      }

      stop() {
        this.isRunning = false;
        if (this.updateInterval) {
          clearInterval(this.updateInterval);
          this.updateInterval = null;
        }
        return this.getElapsedTime();
      }

      reset() {
        this.stop();
        this.startTime = null;
        this.hide();
      }

      show() {
        this.display.classList.remove('hidden');
      }

      hide() {
        this.display.classList.add('hidden');
      }

      getElapsedTime() {
        if (!this.startTime) return 0;
        return (Date.now() - this.startTime) / 1000;
      }

      updateDisplay() {
        if (!this.isRunning || !this.startTime) return;

        const elapsed = this.getElapsedTime();
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;

        this.display.textContent =
          `${minutes.toString().padStart(2, '0')}:${seconds.toFixed(1).padStart(4, '0')}`;
      }
    }

    // ======================
    // TRAINING SYSTEM
    // ======================

    class LookaheadTrainer {
      constructor(cube) {
        this.cube = cube;
        this.timer = new GameTimer();
        this.gameState = 'IDLE'; // IDLE, PIECE_SELECTED, SCRAMBLE_SHOWN, AWAITING_GUESS, EVALUATING
        this.currentScramble = [];
        this.originalCubelet = null;
        this.originalSticker = null;
        this.userGuess = null;
        this.correctAnswer = null;
        this.currentHighlight = null;

        this.updateUI();
      }

      // ======================
      // GAME STATE MANAGEMENT
      // ======================

      updateUI() {
        const randomButton = document.getElementById('randomButton');
        const startButton = document.getElementById('startButton');
        const revealButton = document.getElementById('revealButton');
        const resetButton = document.getElementById('resetButton');
        const scrambleOverlay = document.getElementById('scrambleOverlay');

        // Hide all buttons first
        [randomButton, startButton, revealButton].forEach(btn => btn.classList.add('hidden'));

        switch (this.gameState) {
          case 'IDLE':
            randomButton.classList.remove('hidden');
            resetButton.classList.remove('hidden');
            scrambleOverlay.classList.add('hidden');
            break;
          case 'PIECE_SELECTED':
            randomButton.classList.remove('hidden');
            startButton.classList.remove('hidden');
            resetButton.classList.remove('hidden');
            scrambleOverlay.classList.add('hidden');
            break;
          case 'SCRAMBLE_SHOWN':
            resetButton.classList.remove('hidden');
            scrambleOverlay.classList.remove('hidden');
            break;
          case 'AWAITING_GUESS':
            revealButton.classList.remove('hidden');
            resetButton.classList.remove('hidden');
            scrambleOverlay.classList.remove('hidden');
            break;
          case 'EVALUATING':
            resetButton.classList.remove('hidden');
            scrambleOverlay.classList.remove('hidden');
            break;
        }
      }

      selectRandomPiece() {
        // Get all valid cubelets (edges and corners)
        const validCubelets = this.cube.cubelets.filter(cubelet => {
          const nonZeroAxes = cubelet.userData.position.filter(v => v !== 0);
          return nonZeroAxes.length > 1; // Edge or corner pieces
        });

        if (validCubelets.length === 0) return;

        // Select random cubelet
        const randomCubelet = validCubelets[Math.floor(Math.random() * validCubelets.length)];

        // Get all stickers on this cubelet
        const stickers = randomCubelet.children.filter(child =>
          child.type === 'Mesh' && child.geometry.type === 'PlaneGeometry'
        );

        if (stickers.length === 0) return;

        // Select random sticker
        const randomSticker = stickers[Math.floor(Math.random() * stickers.length)];

        // Update selection
        this.cube.updateSelection(randomCubelet, randomSticker);
        this.gameState = 'PIECE_SELECTED';
        this.updateUI();
        this.updateStatus('Random piece selected! Click "Start Round" to begin.');
      }

      startRound() {
        if (this.gameState !== 'PIECE_SELECTED') {
          this.updateStatus('Please select a piece first!');
          return;
        }

        // Store references to the original cubelet and sticker
        if (this.cube.selectedCubelet && this.cube.selectedCubelet.userData.originalSticker) {
          this.originalCubelet = this.cube.selectedCubelet;
          this.originalSticker = this.cube.selectedCubelet.userData.originalSticker;
        } else {
          this.updateStatus('Please select a piece first!');
          return;
        }

        const length = parseInt(document.getElementById('scrambleLength').value);
        this.currentScramble = this.generateScramble(length);

        document.getElementById('scrambleOverlay').textContent = this.currentScramble.join(' ');
        this.gameState = 'SCRAMBLE_SHOWN';
        this.updateUI();
        this.updateStatus('Study the scramble, then click anywhere to start the timer and make your prediction.');

        // Add click listener to start prediction phase
        this.addStartPredictionListener();
      }

      addStartPredictionListener() {
        const clickHandler = () => {
          document.removeEventListener('click', clickHandler);
          this.startPredictionPhase();
        };
        document.addEventListener('click', clickHandler);
      }

      startPredictionPhase() {
        this.timer.start();
        this.gameState = 'AWAITING_GUESS';
        this.updateUI();
        this.updateStatus('Timer started! Click on the sticker where you think the selected piece will end up.');
      }

      generateScramble(length = 10) {
        const faces = ['U', 'D', 'R', 'L', 'F', 'B'];
        const modifiers = ['', "'", '2'];
        const scramble = [];

        while (scramble.length < length) {
          const face = faces[Math.floor(Math.random() * faces.length)];
          const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
          const move = face + modifier;

          // Avoid immediate cancellation or repetition
          const lastMove = scramble[scramble.length - 1];
          if (!lastMove || !this.movesConflict(lastMove, move)) {
            scramble.push(move);
          }
        }

        return scramble;
      }

      movesConflict(move1, move2) {
        // Simple conflict detection - same face
        return move1[0] === move2[0];
      }

      // ======================
      // GUESS HANDLING
      // ======================

      handleStickerGuess(guessedCubelet, guessedSticker) {
        if (this.gameState !== 'AWAITING_GUESS') return;

        const elapsedTime = this.timer.stop();

        // Store the user's guess
        this.userGuess = {
          cubelet: guessedCubelet,
          sticker: guessedSticker,
          time: elapsedTime
        };

        this.gameState = 'EVALUATING';
        this.updateUI();
        this.updateStatus('Evaluating your guess...');

        // Calculate the correct answer and compare
        this.evaluateGuess();
      }

      evaluateGuess() {
        // RELIABLE APPROACH: Use logical tracking but compare final positions directly

        // Get the original piece and sticker information
        const originalLogicalPosition = this.originalCubelet.userData.originalLogicalPosition;
        const originalStickerFace = this.getOriginalStickerFace(this.originalSticker);
        const originalFaceNotation = this.stickerFaceToCubeNotation(originalStickerFace);

        console.log('Starting LOGICAL tracking:', {
          originalCubelet: originalLogicalPosition,
          originalStickerFace: originalStickerFace,
          originalFaceNotation: originalFaceNotation,
          scramble: this.currentScramble.join(' ')
        });

        // Create a copy of the cube state to simulate the moves
        const simulatedState = new CubeState();

        // Apply all the scramble moves to the simulated state
        this.currentScramble.forEach(move => {
          simulatedState.applyMove(move);
        });

        // Find where the original piece ended up
        const finalPieceLocation = simulatedState.findPiece(originalLogicalPosition);

        console.log('Logical tracking result:', {
          originalPiece: originalLogicalPosition,
          finalLocation: finalPieceLocation,
          scramble: this.currentScramble.join(' ')
        });

        // Apply the actual moves to the 3D cube for visual feedback
        this.cube.applyMoves(this.currentScramble);

        // Wait for animation to complete, then evaluate
        const checkComplete = setInterval(() => {
          if (!this.cube.animating) {
            clearInterval(checkComplete);
            this.evaluateWithSimpleComparison(finalPieceLocation, originalFaceNotation);
          }
        }, 50);
      }

      getOriginalStickerFace(sticker) {
        // Determine which face this sticker is on based on its position relative to parent
        const parent = sticker.parent;
        const stickerPos = sticker.position;

        // Check which face the sticker is on
        if (Math.abs(stickerPos.x) > 0.4) {
          return { axis: 'X', direction: stickerPos.x > 0 ? 1 : -1 };
        } else if (Math.abs(stickerPos.y) > 0.4) {
          return { axis: 'Y', direction: stickerPos.y > 0 ? 1 : -1 };
        } else if (Math.abs(stickerPos.z) > 0.4) {
          return { axis: 'Z', direction: stickerPos.z > 0 ? 1 : -1 };
        }
        return null;
      }

      // Convert 3D sticker face to cube notation (U, D, L, R, F, B)
      stickerFaceToCubeNotation(stickerFace) {
        if (!stickerFace) return null;

        if (stickerFace.axis === 'Y') {
          return stickerFace.direction > 0 ? 'U' : 'D';
        } else if (stickerFace.axis === 'X') {
          return stickerFace.direction > 0 ? 'R' : 'L';
        } else if (stickerFace.axis === 'Z') {
          return stickerFace.direction > 0 ? 'F' : 'B';
        }

        return null;
      }

      evaluateWithSimpleComparison(finalPieceLocation, originalFaceNotation) {
        if (!finalPieceLocation) {
          console.error('Could not find final piece location');
          this.showResults(false, false);
          return;
        }

        // Get the user's guess information
        const userGuessLogicalPosition = this.userGuess.cubelet.userData.logicalPosition;
        const userGuessStickerFace = this.getOriginalStickerFace(this.userGuess.sticker);
        const userGuessFaceNotation = this.stickerFaceToCubeNotation(userGuessStickerFace);

        // Check position correctness
        const locationCorrect = finalPieceLocation.logicalPosition === userGuessLogicalPosition;

        // For orientation, temporarily simplify - if position is correct, consider orientation correct too
        // This prevents false negatives while we work on proper orientation logic
        let stickerCorrect = locationCorrect;
        let stickerResult = null;

        console.log('Orientation check temporarily simplified - position correct = orientation correct');

        console.log('Final evaluation:', {
          locationMatch: locationCorrect ? 'CORRECT' : 'INCORRECT',
          stickerMatch: stickerCorrect ? 'CORRECT' : 'INCORRECT',
          expectedPosition: finalPieceLocation.logicalPosition,
          actualGuessPosition: userGuessLogicalPosition,
          expectedStickerFace: originalFaceNotation,
          actualGuessStickerFace: userGuessFaceNotation,
          finalPieceLocation: finalPieceLocation,
          stickerResult: stickerResult
        });

        // Store the correct answer for highlighting
        this.correctAnswer = {
          logicalPosition: finalPieceLocation.logicalPosition,
          coordinates: finalPieceLocation.coordinates,
          orientation: finalPieceLocation.orientation
        };

        this.showResults(locationCorrect, stickerCorrect);
      }

      evaluateStickerOrientation(finalPieceLocation) {
        if (!this.originalSticker || !this.userGuess.sticker) {
          return false;
        }

        // Simple approach: check if the user clicked on the same sticker face that the original sticker ended up on

        // Get the original sticker face in cube notation
        const originalStickerFace = this.getOriginalStickerFace(this.originalSticker);
        const originalFaceNotation = this.stickerFaceToCubeNotation(originalStickerFace);

        // Get the user's guess sticker face in cube notation
        const guessStickerFace = this.getOriginalStickerFace(this.userGuess.sticker);
        const guessFaceNotation = this.stickerFaceToCubeNotation(guessStickerFace);

        // Check if position is correct first
        const positionCorrect = finalPieceLocation.logicalPosition === this.userGuess.cubelet.userData.logicalPosition;

        if (!positionCorrect) {
          // If position is wrong, orientation doesn't matter
          return false;
        }

        // If position is correct, check if the sticker face matches
        // For a perfect match, the user should click on the same face that the original sticker ended up on

        // Apply the moves to the 3D cube and track where the original sticker actually ends up
        const originalStickerPosition = this.getOriginalStickerFace(this.originalSticker);
        const guessedStickerPosition = this.getOriginalStickerFace(this.userGuess.sticker);

        console.log('Simple sticker orientation check:', {
          originalFaceNotation: originalFaceNotation,
          guessFaceNotation: guessFaceNotation,
          positionCorrect: positionCorrect,
          stickerMatch: originalFaceNotation === guessFaceNotation,
          originalStickerPosition: originalStickerPosition,
          guessedStickerPosition: guessedStickerPosition
        });

        // For now, if the position is correct, consider the orientation correct too
        // This simplifies the logic while we work on proper sticker tracking
        return true;
      }

      arraysEqual(a, b) {
        if (!a || !b || a.length !== b.length) return false;
        return a.every((val, index) => val === b[index]);
      }

      rotateFaceAroundAxis(face, axis, direction) {
        // Rotate a face orientation around a given axis
        // direction: 1 = 90° clockwise, -1 = 90° counterclockwise, 2 = 180°

        if (face.axis === axis) {
          // Face is parallel to rotation axis - orientation doesn't change
          return { ...face };
        }

        let newFace = { ...face };

        if (direction === 2) {
          // 180° rotation - flip both perpendicular axes
          if (axis === 'X') {
            if (face.axis === 'Y') newFace.direction = -face.direction;
            if (face.axis === 'Z') newFace.direction = -face.direction;
          } else if (axis === 'Y') {
            if (face.axis === 'X') newFace.direction = -face.direction;
            if (face.axis === 'Z') newFace.direction = -face.direction;
          } else if (axis === 'Z') {
            if (face.axis === 'X') newFace.direction = -face.direction;
            if (face.axis === 'Y') newFace.direction = -face.direction;
          }
        } else {
          // 90° rotation - cycle through axes
          const rotDir = direction; // 1 or -1

          if (axis === 'X') {
            if (face.axis === 'Y') {
              newFace.axis = 'Z';
              newFace.direction = face.direction * rotDir;
            } else if (face.axis === 'Z') {
              newFace.axis = 'Y';
              newFace.direction = face.direction * -rotDir;
            }
          } else if (axis === 'Y') {
            if (face.axis === 'X') {
              newFace.axis = 'Z';
              newFace.direction = face.direction * -rotDir;
            } else if (face.axis === 'Z') {
              newFace.axis = 'X';
              newFace.direction = face.direction * rotDir;
            }
          } else if (axis === 'Z') {
            if (face.axis === 'X') {
              newFace.axis = 'Y';
              newFace.direction = face.direction * rotDir;
            } else if (face.axis === 'Y') {
              newFace.axis = 'X';
              newFace.direction = face.direction * -rotDir;
            }
          }
        }

        return newFace;
      }





      showResults(locationCorrect, stickerCorrect) {
        const time = this.userGuess.time.toFixed(2);
        let message, isSuccess = false;

        if (locationCorrect) {
          message = `🎉 Correct! Right position in ${time}s`;
          isSuccess = true;
        } else {
          message = `❌ Incorrect position. Time: ${time}s`;
        }

        // Highlight the correct answer
        if (this.correctAnswer.logicalPosition) {
          this.highlightCorrectAnswerByLogicalPosition(this.correctAnswer.logicalPosition);
        }

        this.updateStatus(message);

        // Auto-reset after a delay or wait for manual reset
        if (isSuccess) {
          setTimeout(() => {
            this.reset();
          }, 3000);
        }
      }

      revealAnswer() {
        if (this.gameState !== 'AWAITING_GUESS') return;

        this.timer.stop();
        this.gameState = 'EVALUATING';
        this.updateUI();
        this.updateStatus('Revealing answer...');

        // Calculate and show the correct answer without user guess
        this.showAnswerOnly();
      }

      showAnswerOnly() {
        // Use logical tracking to find the answer

        const originalLogicalPosition = this.originalCubelet.userData.originalLogicalPosition;

        console.log('Revealing answer with logical tracking');

        // Create a copy of the cube state to simulate the moves
        const simulatedState = new CubeState();

        // Apply all the scramble moves to the simulated state
        this.currentScramble.forEach(move => {
          simulatedState.applyMove(move);
        });

        // Find where the original piece ended up
        const finalPieceLocation = simulatedState.findPiece(originalLogicalPosition);

        // Apply the actual moves to the 3D cube for visual feedback
        this.cube.applyMoves(this.currentScramble);

        // Wait for animation to complete, then highlight the answer
        const checkComplete = setInterval(() => {
          if (!this.cube.animating) {
            clearInterval(checkComplete);
            this.displayLogicalAnswer(finalPieceLocation);
          }
        }, 50);
      }

      displayStickerAnswer() {
        // Find the tracked sticker after all moves
        let trackedSticker = null;
        let trackedCubelet = null;

        // Search through all cubelets to find the tracked sticker
        for (const cubelet of this.cube.cubelets) {
          for (const child of cubelet.children) {
            if (child.userData && child.userData._isTrackedSticker) {
              trackedSticker = child;
              trackedCubelet = cubelet;
              break;
            }
          }
          if (trackedSticker) break;
        }

        if (trackedSticker && trackedCubelet) {
          // Clean up tracking flag
          trackedSticker.userData._isTrackedSticker = false;

          // Highlight the tracked sticker specifically
          this.highlightTrackedSticker(trackedSticker);
          this.updateStatus(`💡 Answer revealed! The selected sticker ends up at ${trackedCubelet.userData.logicalPosition}.`);
        } else {
          this.updateStatus('❌ Error: Could not find tracked sticker');
        }
      }

      displayLogicalAnswer(finalPieceLocation) {
        if (finalPieceLocation && finalPieceLocation.logicalPosition) {
          this.highlightCorrectAnswerByLogicalPosition(finalPieceLocation.logicalPosition);
          this.updateStatus(`💡 Answer revealed! The selected piece ends up at ${finalPieceLocation.logicalPosition}.`);
        } else {
          this.updateStatus('❌ Error: Could not determine final position');
        }
      }

      // ======================
      // UTILITY METHODS
      // ======================

      highlightCorrectAnswer(sticker) {
        // Briefly highlight the correct sticker
        const originalColor = sticker.material.color.getHex();
        sticker.material.color.setHex(0x00ff00); // Green

        setTimeout(() => {
          sticker.material.color.setHex(originalColor);
        }, 3000);
      }

      highlightCorrectAnswerByPosition(coordinates) {
        // Find the cubelet at the correct position and highlight it
        const targetCubelet = this.cube.cubelets.find(cubelet => {
          return this.arraysEqual(cubelet.userData.position, coordinates);
        });

        if (targetCubelet) {
          // Highlight the entire cubelet
          const originalColor = targetCubelet.material.color.getHex();
          targetCubelet.material.color.setHex(0x00ff00); // Green
          targetCubelet.material.emissive.setHex(0x004400);
          targetCubelet.material.emissiveIntensity = 0.3;

          setTimeout(() => {
            targetCubelet.material.color.setHex(originalColor);
            targetCubelet.material.emissive.setHex(0x000000);
            targetCubelet.material.emissiveIntensity = 0;
          }, 3000);
        }
      }

      highlightCorrectAnswerByLogicalPosition(logicalPosition) {
        // Clear any existing highlights first
        this.clearAllHighlights();

        // Find the cubelet with the correct logical position and highlight it
        const targetCubelet = this.cube.cubelets.find(cubelet => {
          return cubelet.userData.logicalPosition === logicalPosition;
        });

        if (targetCubelet) {
          // Store reference for cleanup
          this.currentHighlight = targetCubelet;

          // Highlight the entire cubelet
          const originalColor = targetCubelet.material.color.getHex();
          targetCubelet.material.color.setHex(0x00ff00); // Green
          targetCubelet.material.emissive.setHex(0x004400);
          targetCubelet.material.emissiveIntensity = 0.3;

          setTimeout(() => {
            if (this.currentHighlight === targetCubelet) {
              targetCubelet.material.color.setHex(originalColor);
              targetCubelet.material.emissive.setHex(0x000000);
              targetCubelet.material.emissiveIntensity = 0;
              this.currentHighlight = null;
            }
          }, 3000);
        }
      }

      highlightTrackedSticker(sticker) {
        // Clear any existing highlights first
        this.clearAllHighlights();

        if (sticker) {
          // Highlight the specific sticker
          const originalColor = sticker.material.color.getHex();
          sticker.material.color.setHex(0x00ff00); // Green

          setTimeout(() => {
            const showColors = document.getElementById('toggleColors')?.checked ?? true;
            if (showColors && sticker.userData.originalColor !== undefined) {
              sticker.material.color.setHex(sticker.userData.originalColor);
            } else {
              sticker.material.color.setHex(0xcccccc); // Grey when colors are off
            }
          }, 3000);
        }
      }

      clearAllHighlights() {
        // Clear any existing highlights
        if (this.currentHighlight) {
          this.currentHighlight.material.color.setHex(0x222222);
          this.currentHighlight.material.emissive.setHex(0x000000);
          this.currentHighlight.material.emissiveIntensity = 0;
          this.currentHighlight = null;
        }
      }

      updateStatus(message) {
        document.getElementById('status').textContent = message;
      }

      reset() {
        this.timer.reset();
        this.gameState = 'IDLE';
        this.currentScramble = [];
        this.originalCubelet = null;
        this.originalSticker = null;
        this.userGuess = null;
        this.correctAnswer = null;

        // Clear any existing highlights
        this.clearAllHighlights();

        document.getElementById('scrambleOverlay').textContent = '';
        this.updateUI();
        this.updateStatus('Click a piece/sticker to select it, or use "Random Start"');
        this.cube.reset();
      }

      // ======================
      // CUBE INTERACTION BRIDGE
      // ======================

      onPieceSelected() {
        if (this.gameState === 'IDLE') {
          this.gameState = 'PIECE_SELECTED';
          this.updateUI();
          this.updateStatus('Piece selected! Click "Start Round" to begin.');
        }
      }

      // This method is called by the cube when a sticker is clicked during guessing
      get awaitingUserGuess() {
        return this.gameState === 'AWAITING_GUESS';
      }
    }

    // ======================
    // MAIN APPLICATION
    // ======================

    // Initialize the application
    const renderer = new CubeRenderer();
    const cube = new RubiksCube(renderer);
    const trainer = new LookaheadTrainer(cube);

    // Set up color toggle event
    document.getElementById('toggleColors').addEventListener('change', () => {
      cube.toggleColors();
    });

    // Main animation loop
    function animate() {
      requestAnimationFrame(animate);
      cube.updateAnimation();
      renderer.render();
    }

    // Start the application
    animate();

    // Expose global functions for HTML buttons
    window.cube = cube;
    window.trainer = trainer;
  </script>
</body>
</html>